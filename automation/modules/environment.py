#!/usr/bin/env python3
import os
import sys
import logging
import subprocess
import shutil
import platform
from pathlib import Path

logger = logging.getLogger('environment')

class EnvironmentManager:
    """Manages the automation environment setup and validation"""
    
    def __init__(self):
        self.required_tools = {
            'aws': 'AWS CLI',
            'terraform': 'Terraform',
            'kubectl': 'Kubernetes CLI',
            'helm': 'Helm',
            'gh': 'GitHub CLI'
        }
        self.venv_path = Path("automation/venv")
        
    def check_environment(self):
        """Check if all required tools are installed"""
        logger.info("Checking environment...")
        
        # Check Python version
        python_version = platform.python_version_tuple()
        if int(python_version[0]) < 3 or (int(python_version[0]) == 3 and int(python_version[1]) < 8):
            logger.error("Python 3.8+ is required")
            return False
            
        # Check virtual environment
        if not self.venv_path.exists():
            logger.info("Virtual environment not found")
            return False
            
        # Check required tools
        missing_tools = []
        for tool, name in self.required_tools.items():
            if not shutil.which(tool):
                missing_tools.append(name)
                
        if missing_tools:
            logger.error(f"Missing required tools: {', '.join(missing_tools)}")
            return False
            
        return True
        
    def setup(self):
        """Set up the automation environment"""
        logger.info("Setting up automation environment...")
        
        # Create virtual environment if it doesn't exist
        if not self.venv_path.exists():
            logger.info("Creating virtual environment...")
            try:
                subprocess.run([sys.executable, "-m", "venv", str(self.venv_path)], check=True)
                logger.info("Virtual environment created successfully")
            except subprocess.CalledProcessError:
                logger.error("Failed to create virtual environment")
                return False
                
        # Install required Python packages
        pip_path = str(self.venv_path / "bin" / "pip") if platform.system() != "Windows" else str(self.venv_path / "Scripts" / "pip")
        
        try:
            logger.info("Installing required Python packages...")
            subprocess.run([pip_path, "install", "-U", "pip"], check=True)
            subprocess.run([pip_path, "install", "boto3", "requests", "pyyaml", "tqdm"], check=True)
            logger.info("Python packages installed successfully")
        except subprocess.CalledProcessError:
            logger.error("Failed to install Python packages")
            return False
            
        # Check and install required tools
        self._check_and_install_tools()
        
        return True
        
    def _check_and_install_tools(self):
        """Check and provide instructions for installing missing tools"""
        for tool, name in self.required_tools.items():
            if not shutil.which(tool):
                logger.warning(f"{name} not found. Please install it.")
                self._print_install_instructions(tool)
                
    def _print_install_instructions(self, tool):
        """Print installation instructions for a specific tool"""
        system = platform.system().lower()
        
        instructions = {
            "aws": {
                "linux": "pip install awscli or sudo apt install awscli",
                "darwin": "brew install awscli",
                "windows": "pip install awscli or choco install awscli"
            },
            "terraform": {
                "linux": "https://developer.hashicorp.com/terraform/install",
                "darwin": "brew install terraform",
                "windows": "choco install terraform"
            },
            "kubectl": {
                "linux": "sudo apt install kubectl",
                "darwin": "brew install kubectl",
                "windows": "choco install kubernetes-cli"
            },
            "helm": {
                "linux": "curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash",
                "darwin": "brew install helm",
                "windows": "choco install kubernetes-helm"
            },
            "gh": {
                "linux": "sudo apt install gh",
                "darwin": "brew install gh",
                "windows": "winget install GitHub.cli"
            }
        }
        
        if system in instructions[tool]:
            print(f"To install {tool}, run: {instructions[tool][system]}")
        else:
            print(f"Please install {tool} according to your system's package manager")

def setup_prerequisites():
    """Set up all prerequisites for the project"""
    logger.info("Setting up prerequisites...")
    
    # Create S3 bucket for Terraform state
    _setup_terraform_backend()
    
    # Set up GitHub secrets
    _setup_github_secrets()
    
    # Set up GitHub branch protection
    _setup_branch_protection()
    
    logger.info("Prerequisites setup completed")

def _setup_terraform_backend():
    """Set up S3 bucket and DynamoDB table for Terraform state"""
    from tqdm import tqdm
    import boto3
    import time
    
    logger.info("Setting up Terraform backend...")
    
    # Create S3 bucket
    s3_client = boto3.client('s3')
    bucket_name = "ce-grp-1-tfstate"
    
    try:
        logger.info(f"Creating S3 bucket: {bucket_name}")
        with tqdm(total=100, desc="Creating S3 bucket") as pbar:
            s3_client.create_bucket(Bucket=bucket_name)
            pbar.update(50)
            
            # Enable versioning
            s3_client.put_bucket_versioning(
                Bucket=bucket_name,
                VersioningConfiguration={'Status': 'Enabled'}
            )
            pbar.update(25)
            
            # Enable encryption
            s3_client.put_bucket_encryption(
                Bucket=bucket_name,
                ServerSideEncryptionConfiguration={
                    'Rules': [
                        {
                            'ApplyServerSideEncryptionByDefault': {
                                'SSEAlgorithm': 'AES256'
                            }
                        }
                    ]
                }
            )
            pbar.update(25)
            
        logger.info(f"S3 bucket {bucket_name} created successfully")
    except Exception as e:
        if "BucketAlreadyOwnedByYou" in str(e):
            logger.info(f"S3 bucket {bucket_name} already exists")
        else:
            logger.error(f"Failed to create S3 bucket: {e}")
            return False
    
    # Create DynamoDB table
    dynamodb_client = boto3.client('dynamodb')
    table_name = "ce-grp-1-tf-locks"
    
    try:
        logger.info(f"Creating DynamoDB table: {table_name}")
        with tqdm(total=100, desc="Creating DynamoDB table") as pbar:
            dynamodb_client.create_table(
                TableName=table_name,
                KeySchema=[
                    {
                        'AttributeName': 'LockID',
                        'KeyType': 'HASH'
                    }
                ],
                AttributeDefinitions=[
                    {
                        'AttributeName': 'LockID',
                        'AttributeType': 'S'
                    }
                ],
                BillingMode='PAY_PER_REQUEST'
            )
            pbar.update(50)
            
            # Wait for table to be created
            time.sleep(5)
            pbar.update(50)
            
        logger.info(f"DynamoDB table {table_name} created successfully")
    except Exception as e:
        if "ResourceInUseException" in str(e):
            logger.info(f"DynamoDB table {table_name} already exists")
        else:
            logger.error(f"Failed to create DynamoDB table: {e}")
            return False
    
    # Create backend.tf file
    backend_dir = Path("terraform/vpc")
    backend_dir.mkdir(parents=True, exist_ok=True)
    
    backend_file = backend_dir / "backend.tf"
    backend_content = """
terraform {
  backend "s3" {
    bucket         = "ce-grp-1-tfstate"
    key            = "vpc/terraform.tfstate"
    region         = "us-east-1"
    dynamodb_table = "ce-grp-1-tf-locks"
    encrypt        = true
  }
}
"""
    
    with open(backend_file, "w") as f:
        f.write(backend_content)
    
    logger.info("Terraform backend setup completed")
    return True

def _setup_github_secrets():
    """Set up GitHub secrets for AWS credentials"""
    logger.info("Setting up GitHub secrets...")
    
    # Check if GitHub CLI is installed and authenticated
    try:
        subprocess.run(["gh", "auth", "status"], check=True, capture_output=True)
    except subprocess.CalledProcessError:
        logger.error("GitHub CLI not authenticated. Please run 'gh auth login' first")
        return False
    
    # Prompt for AWS credentials
    print("\nPlease enter your AWS credentials to set up GitHub secrets:")
    aws_access_key = input("AWS Access Key ID: ")
    aws_secret_key = input("AWS Secret Access Key: ")
    
    if not aws_access_key or not aws_secret_key:
        logger.error("AWS credentials cannot be empty")
        return False
    
    # Set GitHub secrets
    try:
        logger.info("Setting AWS_ACCESS_KEY_ID secret...")
        subprocess.run(["gh", "secret", "set", "AWS_ACCESS_KEY_ID", "-b", aws_access_key], check=True)
        
        logger.info("Setting AWS_SECRET_ACCESS_KEY secret...")
        subprocess.run(["gh", "secret", "set", "AWS_SECRET_ACCESS_KEY", "-b", aws_secret_key], check=True)
        
        logger.info("GitHub secrets set successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to set GitHub secrets: {e}")
        return False

def _setup_branch_protection():
    """Set up GitHub branch protection rules"""
    logger.info("Setting up GitHub branch protection rules...")
    
    # This requires GitHub CLI and API access
    # For simplicity, we'll just provide instructions
    print("\nTo set up branch protection rules:")
    print("1. Go to your GitHub repository")
    print("2. Click on Settings > Branches")
    print("3. Under 'Branch protection rules', click 'Add rule'")
    print("4. Set 'Branch name pattern' to 'main'")
    print("5. Enable 'Require a pull request before merging'")
    print("6. Enable 'Require approvals'")
    print("7. Enable 'Require status checks to pass before merging'")
    print("8. Save changes")
    
    logger.info("Branch protection setup instructions provided")
    return True