#!/usr/bin/env python3
import os
import logging
import subprocess
import json
from pathlib import Path
from tqdm import tqdm
import time

logger = logging.getLogger('vpc')

def check_status():
    """Check if VPC is already deployed"""
    logger.info("Checking VPC deployment status...")
    
    try:
        # Initialize Terraform
        os.chdir("terraform/vpc")
        subprocess.run(["terraform", "init"], check=True, capture_output=True)
        
        # Get Terraform state
        result = subprocess.run(
            ["terraform", "state", "list"], 
            check=False, 
            capture_output=True, 
            text=True
        )
        
        # Change back to original directory
        os.chdir("../..")
        
        # Check if VPC module is in state
        if "module.vpc" in result.stdout:
            logger.info("VPC is already deployed")
            return True
        else:
            logger.info("VPC is not deployed")
            return False
    except subprocess.CalledProcessError as e:
        logger.error(f"Error checking VPC status: {e}")
        # Change back to original directory
        os.chdir("../..")
        return False

def deploy():
    """Deploy VPC infrastructure"""
    logger.info("Deploying VPC infrastructure...")
    
    # Ensure terraform/vpc directory exists
    vpc_dir = Path("terraform/vpc")
    vpc_dir.mkdir(parents=True, exist_ok=True)
    
    # Create main.tf if it doesn't exist
    main_tf = vpc_dir / "main.tf"
    if not main_tf.exists():
        _create_vpc_terraform_files()
    
    try:
        # Change to terraform/vpc directory
        os.chdir("terraform/vpc")
        
        # Initialize Terraform
        logger.info("Initializing Terraform...")
        with tqdm(total=100, desc="Terraform Init") as pbar:
            subprocess.run(["terraform", "init"], check=True, capture_output=True)
            pbar.update(100)
        
        # Format Terraform files
        logger.info("Formatting Terraform files...")
        with tqdm(total=100, desc="Terraform Format") as pbar:
            subprocess.run(["terraform", "fmt"], check=True, capture_output=True)
            pbar.update(100)
        
        # Validate Terraform files
        logger.info("Validating Terraform files...")
        with tqdm(total=100, desc="Terraform Validate") as pbar:
            subprocess.run(["terraform", "validate"], check=True, capture_output=True)
            pbar.update(100)
        
        # Plan Terraform changes
        logger.info("Planning Terraform changes...")
        with tqdm(total=100, desc="Terraform Plan") as pbar:
            subprocess.run(["terraform", "plan", "-out=tfplan"], check=True, capture_output=True)
            pbar.update(100)
        
        # Apply Terraform changes
        logger.info("Applying Terraform changes...")
        with tqdm(total=100, desc="Terraform Apply") as pbar:
            process = subprocess.Popen(
                ["terraform", "apply", "-auto-approve", "tfplan"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            
            # Monitor progress
            for i, line in enumerate(process.stdout):
                if i % 10 == 0:  # Update progress every 10 lines
                    pbar.update(1)
            
            process.wait()
            pbar.update(100)  # Ensure progress reaches 100%
        
        # Get VPC outputs
        logger.info("Getting VPC outputs...")
        result = subprocess.run(
            ["terraform", "output", "-json"],
            check=True,
            capture_output=True,
            text=True
        )
        
        # Save outputs to file
        outputs = json.loads(result.stdout)
        with open("vpc_outputs.json", "w") as f:
            json.dump(outputs, f, indent=2)
        
        logger.info("VPC deployed successfully")
        
        # Change back to original directory
        os.chdir("../..")
        return True
    except subprocess.Called