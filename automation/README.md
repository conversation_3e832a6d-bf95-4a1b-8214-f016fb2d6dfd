# CE-GRP-1 DevOps Automation Scripts

This directory contains automation scripts for the CE-GRP-1 DevOps Capstone project. The scripts provide a comprehensive solution for deploying and managing AWS infrastructure using Infrastructure as Code (IaC) principles.

## 🚀 Overview

The automation tool provides an interactive menu-driven interface to deploy and manage:
- VPC and networking infrastructure
- EKS (Elastic Kubernetes Service) clusters
- Ingress controllers and load balancers
- CI/CD pipelines with GitHub Actions
- Monitoring stack (Prometheus)
- Sample applications

## 📁 Project Structure

```
automation/
├── main.py                 # Main automation script with interactive menu
├── modules/                # Automation modules
│   ├── environment.py      # Environment setup and validation
│   ├── vpc.py             # VPC deployment and management
│   ├── eks.py             # EKS cluster management (referenced)
│   ├── cicd.py            # CI/CD pipeline setup (referenced)
│   ├── monitoring.py      # Monitoring stack deployment (referenced)
│   ├── ingress.py         # Ingress controller setup (referenced)
│   └── application.py     # Sample application deployment (referenced)
└── README.md              # This file
```

## 🛠️ Prerequisites

### Required Tools
- **Python 3.8+** - Programming language runtime
- **AWS CLI** - AWS command line interface
- **Terraform** - Infrastructure as Code tool
- **kubectl** - Kubernetes command line tool
- **Helm** - Kubernetes package manager
- **GitHub CLI** - GitHub command line interface

### AWS Requirements
- AWS account with appropriate permissions
- AWS credentials configured (Access Key ID and Secret Access Key)
- S3 bucket for Terraform state storage
- DynamoDB table for Terraform state locking

## 📦 Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd ce-grp-1-vpc/automation
```

### 2. Run the Automation Tool
```bash
python3 main.py
```

The tool will automatically:
- Check for required dependencies
- Create a Python virtual environment
- Install required Python packages (boto3, requests, pyyaml, tqdm)
- Provide installation instructions for missing tools

### 3. Manual Tool Installation (if needed)

#### macOS (using Homebrew)
```bash
brew install awscli terraform kubectl helm gh
```

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install awscli kubectl gh
# Terraform and Helm require manual installation - see tool instructions
```

#### Windows (using Chocolatey)
```bash
choco install awscli terraform kubernetes-cli kubernetes-helm
winget install GitHub.cli
```

## 🎯 Usage

### Interactive Menu
Run the main script to access the interactive menu:

```bash
python3 main.py
```

### Available Options

1. **Setup Prerequisites**
   - Creates S3 bucket for Terraform state
   - Sets up DynamoDB table for state locking
   - Configures GitHub secrets for AWS credentials
   - Provides branch protection setup instructions

2. **Deploy VPC & Networking**
   - Deploys AWS VPC with public/private subnets
   - Configures NAT gateways and internet gateways
   - Sets up route tables and security groups

3. **Deploy EKS Cluster**
   - Creates managed EKS cluster
   - Configures worker node groups
   - Sets up cluster networking and security

4. **Configure Ingress & Load Balancer**
   - Deploys ALB Ingress Controller
   - Configures Application Load Balancer
   - Sets up ingress rules and SSL certificates

5. **Setup CI/CD with GitHub Actions**
   - Configures GitHub Actions workflows
   - Sets up automated testing and deployment
   - Integrates with AWS services

6. **Deploy Monitoring Stack**
   - Installs Prometheus for metrics collection
   - Sets up Grafana for visualization
   - Configures alerting rules

7. **Deploy Sample Application**
   - Deploys sample static website
   - Configures ingress and load balancing
   - Sets up health checks

8. **Destroy Infrastructure**
   - Provides granular destruction options
   - Safely removes resources in correct order
   - Includes confirmation prompts

## 🔧 Modules

### environment.py
**EnvironmentManager Class**
- Validates Python version and required tools
- Creates and manages Python virtual environment
- Installs required Python packages
- Provides platform-specific installation instructions
- Sets up Terraform backend (S3 + DynamoDB)
- Configures GitHub secrets and branch protection

**Key Functions:**
- `check_environment()` - Validates setup
- `setup()` - Initializes environment
- `setup_prerequisites()` - Configures project prerequisites

### vpc.py
**VPC Management**
- Deploys AWS VPC infrastructure using Terraform
- Manages VPC lifecycle (create, update, destroy)
- Provides deployment status checking
- Handles Terraform state management

**Key Functions:**
- `deploy()` - Deploys VPC infrastructure
- `check_status()` - Checks deployment status
- `destroy()` - Removes VPC resources

## ✨ Features

- **Interactive Menu System** - User-friendly command-line interface
- **Dependency Validation** - Automatic checking of required tools
- **Progress Tracking** - Visual progress bars for long-running operations
- **Error Handling** - Comprehensive error handling and logging
- **State Management** - Terraform state stored in S3 with DynamoDB locking
- **Cross-Platform Support** - Works on Linux, macOS, and Windows
- **Modular Design** - Easily extensible module system
- **Safety Checks** - Prevents destructive operations without confirmation

## 📝 Logging

The automation tool creates detailed logs:
- **Console Output** - Real-time progress and status updates
- **Log File** - Detailed logs saved to `automation.log`
- **Structured Logging** - Timestamp, module, and severity level included

## 🔒 Security

- AWS credentials are handled securely through GitHub secrets
- Terraform state is encrypted in S3
- Branch protection rules prevent unauthorized changes
- Infrastructure follows AWS security best practices

## 🐛 Troubleshooting

### Common Issues

1. **Missing Tools Error**
   - Run option 1 to check environment
   - Follow provided installation instructions
   - Restart terminal after installation

2. **AWS Credentials Error**
   - Ensure AWS CLI is configured: `aws configure`
   - Verify credentials have required permissions
   - Check GitHub secrets are properly set

3. **Terraform State Issues**
   - Ensure S3 bucket exists and is accessible
   - Verify DynamoDB table is created
   - Check AWS region configuration

4. **Permission Denied Errors**
   - Verify AWS IAM permissions
   - Check file system permissions
   - Ensure virtual environment is activated

## 🤝 Contributing

1. Follow the existing code structure and naming conventions
2. Add comprehensive error handling and logging
3. Update this README when adding new features
4. Test on multiple platforms when possible
5. Use type hints and docstrings for new functions

## 📄 License

This project is part of the CE-GRP-1 DevOps Capstone program.
