#!/usr/bin/env python3
import os
import sys
import logging
from pathlib import Path
from modules import environment, vpc, eks, cicd, monitoring, ingress

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('automation.log')
    ]
)
logger = logging.getLogger('main')

def setup_environment():
    """Ensure automation environment is ready"""
    env_manager = environment.EnvironmentManager()
    if not env_manager.check_environment():
        logger.info("Setting up automation environment...")
        env_manager.setup()
    else:
        logger.info("Environment already set up and ready")

def display_menu():
    """Display the main menu options"""
    print("\n===== CE-GRP-1 DevOps Capstone Automation =====")
    print("1. Setup Prerequisites")
    print("2. Deploy VPC & Networking")
    print("3. Deploy EKS Cluster")
    print("4. Configure Ingress & Load Balancer")
    print("5. Setup CI/CD with GitHub Actions")
    print("6. Deploy Monitoring Stack")
    print("7. Deploy Sample Application")
    print("8. Destroy Infrastructure")
    print("9. Exit")
    return input("\nSelect an option (1-9): ")

def main():
    # Ensure automation directory exists
    Path("automation").mkdir(exist_ok=True)
    
    # Setup environment first
    setup_environment()
    
    while True:
        choice = display_menu()
        
        if choice == '1':
            environment.setup_prerequisites()
        elif choice == '2':
            vpc.deploy()
        elif choice == '3':
            if not vpc.check_status():
                logger.error("VPC must be deployed first. Please run option 2.")
                continue
            eks.deploy()
        elif choice == '4':
            if not eks.check_status():
                logger.error("EKS must be deployed first. Please run option 3.")
                continue
            ingress.deploy()
        elif choice == '5':
            cicd.setup()
        elif choice == '6':
            if not eks.check_status():
                logger.error("EKS must be deployed first. Please run option 3.")
                continue
            monitoring.deploy()
        elif choice == '7':
            if not (eks.check_status() and ingress.check_status()):
                logger.error("EKS and Ingress must be deployed first.")
                continue
            from modules import application
            application.deploy()
        elif choice == '8':
            destroy_menu()
        elif choice == '9':
            logger.info("Exiting automation tool")
            sys.exit(0)
        else:
            logger.warning("Invalid option. Please try again.")

def destroy_menu():
    """Submenu for destroy operations"""
    print("\n===== Destroy Infrastructure =====")
    print("1. Destroy Sample Application")
    print("2. Destroy Monitoring Stack")
    print("3. Destroy Ingress & Load Balancer")
    print("4. Destroy EKS Cluster")
    print("5. Destroy VPC & Networking")
    print("6. Destroy All")
    print("7. Back to Main Menu")
    
    choice = input("\nSelect an option (1-7): ")
    
    if choice == '1':
        from modules import application
        application.destroy()
    elif choice == '2':
        monitoring.destroy()
    elif choice == '3':
        ingress.destroy()
    elif choice == '4':
        eks.destroy()
    elif choice == '5':
        vpc.destroy()
    elif choice == '6':
        logger.warning("This will destroy ALL infrastructure. Are you sure? (yes/no)")
        confirm = input().lower()
        if confirm == 'yes':
            from modules import application
            application.destroy()
            monitoring.destroy()
            ingress.destroy()
            eks.destroy()
            vpc.destroy()
    elif choice == '7':
        return
    else:
        logger.warning("Invalid option. Returning to main menu.")

if __name__ == "__main__":
    main()