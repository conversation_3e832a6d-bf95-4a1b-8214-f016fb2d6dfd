#Solution: Use GitHub Actions environment with required approval

GitHub environments let you:
Require manual approval before a job runs

Limit who can approve

🛠️ Step-by-Step: Secure Terraform Destroy with Approval
✅ Step 1: Create an Environment in GitHub
Go to your repo: ce-grp-1-vpc
Click Settings → Environments
Click “New environment"

Name it: manual-approval

Under Required reviewers, add yourself or teammates

✅ Step 2: Update terraform-vpc-destroy.yml
yaml
name: Terraform VPC Destroy (with <PERSON><PERSON><PERSON><PERSON>)

on:
  workflow_dispatch:

env:
  AWS_REGION: us-east-1

jobs:
  terraform-destroy:
    name: Destroy VPC Infrastructure
    runs-on: ubuntu-latest
    environment: manual-approval   # ← 🔒 Require approval before running

    defaults:
      run:
        working-directory: terraform/vpc

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.6.6

      - name: Terraform Init
        run: terraform init

      - name: Terraform Destroy
        run: terraform destroy -auto-approve -input=false

✅ What Happens Now?
Workflow is only triggered manually
GitHub will pause the job at manual-approval until:
An authorized user clicks “Review deployments” → “Approve and deploy”
After approval, the destroy job executes