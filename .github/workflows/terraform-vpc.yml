---
name: Terraform VPC Deployment with Snyk

on:
  push:
    branches: [dev]
  pull_request:
    branches: [main]

env:
  AWS_REGION: us-east-1

jobs:
  terraform:
    name: Terraform VPC
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: terraform/vpc

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.6.6

      - name: Terraform Init
        run: terraform init

      - name: Terraform Format Check
        run: terraform fmt -check

      - name: Terraform Validate
        run: terraform validate

      - name: Terraform Plan
        run: terraform plan -input=false

      - name: Terraform Apply
        if: github.event_name == 'pull_request' && github.base_ref == 'main'
        run: terraform apply -auto-approve -input=false

  snyk-iac:
    name: Snyk IaC Scan
    runs-on: ubuntu-latest
    needs: terraform

    defaults:
      run:
        working-directory: terraform/vpc

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Snyk IaC Security Scan
        uses: snyk/actions/iac@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: "--severity-threshold=medium --org=aalimsee"
