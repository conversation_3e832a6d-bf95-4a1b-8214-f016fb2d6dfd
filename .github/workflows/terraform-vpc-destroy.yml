---
name: Terraform VPC Destroy (with Approval)

on:
  workflow_dispatch: 

env:
  AWS_REGION: us-east-1

jobs:
  terraform-destroy:
    name: Destroy VPC Infrastructure
    runs-on: ubuntu-latest
    environment: manual-approval   # ← 🔒 Require approval before running

    defaults:
      run:
        working-directory: terraform/vpc

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.6.6

      - name: Terraform Init
        run: terraform init

      - name: Terraform Destroy
        run: terraform destroy -auto-approve -input=false

